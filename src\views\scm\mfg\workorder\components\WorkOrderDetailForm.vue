<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    v-loading="formLoading"
    label-width="0px"
    :inline-message="true"
  >
    <el-table
      :data="formData"
      class="-mt-10px"
      border
      :max-height="400"
      style="width: 100%"
      show-summary
      :summary-method="getSummaries"
    >
      <!-- <el-table-column label="序号" type="index" width="100" /> -->
      <!-- <el-table-column label="序号" min-width="80">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.num`" :rules="formRules.num" class="mb-0px!">
            <el-input v-model="row.num" placeholder="序号" disabled />
          </el-form-item>
        </template>
      </el-table-column> -->
      <el-table-column label="物料信息" min-width="200">
        <template #default="{ row }">
          <div class="material-info">
            <div class="material-name">
              <span class="label">名称：</span>
              <span class="value">{{ row.materialName || '-' }}</span>
            </div>
            <div class="material-code">
              <span class="label">编号：</span>
              <span class="value">{{ row.materialCode || '-' }}</span>
            </div>
            <div class="material-spec">
              <span class="label">规格：</span>
              <span class="value">{{ row.spec || '-' }}</span>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="仓库" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.warehouseId`" :rules="formRules.warehouseId" class="mb-0px!">
            <el-tree-select
              v-model="row.warehouseId"
              :data="warehouseTree"
              :props="{ value: 'id', label: 'name', children: 'children' }"
              placeholder="请选择仓库"
              clearable
              filterable
              class="!w-240px"
              node-key="id"
              :render-after-expand="false"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="库位" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.locationId`" :rules="formRules.locationId" class="mb-0px!">
            <el-select
              v-model="row.locationId"
              placeholder="请选择库位"
              class="!w-240px"
            >
              <el-option
                v-for="item in locationList"
                :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>

      <el-table-column label="单位" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.unit`" :rules="formRules.unit" class="mb-0px!">
            <el-select
              v-model="row.unit"
              placeholder="请选择单位"
              class="!w-240px"
              disabled
            >
              <el-option
                v-for="item in unitList"
                :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>
<!--      <el-table-column label="单价" min-width="150">-->
<!--        <template #default="{ row, $index }">-->
<!--          <el-form-item :prop="`${$index}.unitPrice`" :rules="formRules.unitPrice" class="mb-0px!">-->
<!--            <el-input v-model="row.unitPrice" placeholder="请输入单价" />-->
<!--          </el-form-item>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="金额" min-width="150">-->
<!--        <template #default="{ row, $index }">-->
<!--          <el-form-item :prop="`${$index}.amount`" :rules="formRules.amount" class="mb-0px!">-->
<!--            <el-input v-model="row.amount" placeholder="请输入金额" />-->
<!--          </el-form-item>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="备注" min-width="150">-->
<!--        <template #default="{ row, $index }">-->
<!--          <el-form-item :prop="`${$index}.remark`" :rules="formRules.remark" class="mb-0px!">-->
<!--            <el-input v-model="row.remark" placeholder="请输入备注" />-->
<!--          </el-form-item>-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column label="每槽数量" min-width="150" prop="slotQuantity">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.slotQuantity`" :rules="formRules.slotQuantity" class="mb-0px!">
            <el-input-number
              v-model="row.slotQuantity"
              placeholder="请输入每槽数量"
              :precision="4"
              :step="0.0001"
              :min="0"
              controls-position="right"
              class="w-full"
              @change="handleSlotQuantityChange(row)"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <!-- <el-table-column label="每槽规格数量" min-width="150" prop="slotSpecQuantity">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.slotSpecQuantity`" :rules="formRules.slotSpecQuantity" class="mb-0px!">
            <el-input
              v-model="row.slotSpecQuantity"
              placeholder="请输入每槽规格数量"
              controls-position="right"
              class="w-full"
            />
          </el-form-item>
        </template>
      </el-table-column> -->
      <el-table-column label="数量" min-width="150" prop="quantity">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.quantity`" :rules="formRules.quantity" class="mb-0px!">
            <el-input v-model="row.quantity" placeholder="请输入数量" />
          </el-form-item>
        </template>
      </el-table-column>
<!--      <el-table-column label="计划数量" min-width="150">-->
<!--        <template #default="{ row, $index }">-->
<!--          <el-form-item :prop="`${$index}.plannedQuantity`" :rules="formRules.plannedQuantity" class="mb-0px!">-->
<!--            <el-input v-model="row.plannedQuantity" placeholder="请输入计划数量" />-->
<!--          </el-form-item>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="履约数量" min-width="150">-->
<!--        <template #default="{ row, $index }">-->
<!--          <el-form-item :prop="`${$index}.fulfilledQuantity`" :rules="formRules.fulfilledQuantity" class="mb-0px!">-->
<!--            <el-input v-model="row.fulfilledQuantity" placeholder="请输入履约数量" />-->
<!--          </el-form-item>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="基本单位计划数量" min-width="150">-->
<!--        <template #default="{ row, $index }">-->
<!--          <el-form-item :prop="`${$index}.standardPlannedQuantity`" :rules="formRules.standardPlannedQuantity" class="mb-0px!">-->
<!--            <el-input v-model="row.standardPlannedQuantity" placeholder="请输入基本单位计划数量" />-->
<!--          </el-form-item>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="基本单位履约数量" min-width="150">-->
<!--        <template #default="{ row, $index }">-->
<!--          <el-form-item :prop="`${$index}.standardFulfilledQuantity`" :rules="formRules.standardFulfilledQuantity" class="mb-0px!">-->
<!--            <el-input v-model="row.standardFulfilledQuantity" placeholder="请输入基本单位履约数量" />-->
<!--          </el-form-item>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="基本单位" min-width="150">-->
<!--        <template #default="{ row, $index }">-->
<!--          <el-form-item :prop="`${$index}.standardUnit`" :rules="formRules.standardUnit" class="mb-0px!">-->
<!--            <el-select v-model="row.standardUnit" placeholder="请选择基本单位" class="!w-240px">-->
<!--              <el-option-->
<!--                v-for="item in unitList"-->
<!--                :key="item.id" :label="item.name" :value="item.id" />-->
<!--            </el-select>-->
<!--          </el-form-item>-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column label="规格数量" min-width="150" prop="plannedSpecQuantity">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.plannedSpecQuantity`" :rules="formRules.plannedSpecQuantity" class="mb-0px!">
            <el-input v-model="row.plannedSpecQuantity" placeholder="请输入规格数量"/>
          </el-form-item>
        </template>
      </el-table-column>
<!--      <el-table-column label="履约规格数量" min-width="150">-->
<!--        <template #default="{ row, $index }">-->
<!--          <el-form-item :prop="`${$index}.fulfilledSpecQuantity`" :rules="formRules.fulfilledSpecQuantity" class="mb-0px!">-->
<!--            <el-input v-model="row.fulfilledSpecQuantity" placeholder="请输入履约规格数量" />-->
<!--          </el-form-item>-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column label="说明" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.note`" :rules="formRules.note" class="mb-0px!">
            <el-input v-model="row.note" placeholder="请输入说明" />
          </el-form-item>
        </template>
      </el-table-column>
<!--      <el-table-column label="批号" min-width="150">-->
<!--        <template #default="{ row, $index }">-->
<!--          <el-form-item :prop="`${$index}.batchNo`" :rules="formRules.batchNo" class="mb-0px!">-->
<!--            <el-input v-model="row.batchNo" placeholder="请输入批号" />-->
<!--          </el-form-item>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="成本对象编码" min-width="150">-->
<!--        <template #default="{ row, $index }">-->
<!--          <el-form-item :prop="`${$index}.costObjectId`" :rules="formRules.costObjectId" class="mb-0px!">-->
<!--            <el-input v-model="row.costObjectId" placeholder="请输入成本对象编码" />-->
<!--          </el-form-item>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="成本对象名称" min-width="150">-->
<!--        <template #default="{ row, $index }">-->
<!--          <el-form-item :prop="`${$index}.costObjectName`" :rules="formRules.costObjectName" class="mb-0px!">-->
<!--            <el-input v-model="row.costObjectName" placeholder="请输入成本对象名称" />-->
<!--          </el-form-item>-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column align="center" fixed="right" label="操作" width="60">
        <template #default="{ $index }">
          <!-- <el-button @click="handleDelete($index)" link type="danger">—</el-button> -->
           <Icon
             icon="ep:delete"
             color= '#f56c6c'
             style='pointer'
             @click="handleDelete($index)"
           />
        </template>
      </el-table-column>
    </el-table>
  </el-form>
  <el-row justify="center" class="mt-3">
    <el-button
      @click="handleAdd"
      round
    >
      + 添加任务单明细
    </el-button>
  </el-row>
</template>
<script setup lang="ts">
import { WorkOrderApi } from '@/api/scm/mfg/workorder'
import { BomApi } from '@/api/scm/mfg/bom';
import { WarehouseApi } from '@/api/scm/inventory/warehouse';
import { WarehouseLocationApi } from '@/api/scm/inventory/warehouselocation';
import { UnitApi } from '@/api/scm/base/unit';
import { MaterialApi } from '@/api/scm/base/material';
import { handleTree } from '@/utils/tree';
import { formatQuantity, formatAmount } from '@/utils/formatter';
import {
  getUnitConversionFactor,
  parseSpecification,
  formatToPrecision,
  calculateTotalSlots,
  calculateSpecQuantityWithRemainder,
  getUnitDisplayName,
  CALCULATION_CONFIG,
  BUSINESS_RULES_CONFIG,
  type RowData,
  type UnitInfo,
  type SpecInfo,
  type SpecCalculationResult
} from '@/utils/bomCalculation'

const props = defineProps<{
  bizOrderId?: number // 订单编号（主表的关联字段）
  bomId?: number // BOM ID（可选）
  slotQuantity?: number // 每槽数量（主表的关联字段）
  orderQuantity?: number // 订单数量（主表的关联字段）
  orderSpecQuantity?: number // 订单规格数量（主表的关联字段）
  isTransferMode?: boolean // 是否为转生产模式
  unitList?: any[] // 单位列表（从父组件传递，避免重复请求）
}>()
const formLoading = ref(false) // 表单的加载中
const formData = ref<any[]>([])
const formRules = reactive({
})
const formRef = ref() // 表单 Ref

/** 监听主表的关联字段的变化，加载对应的子表数据 */
watch(
  () => ({
    orderId: props.bizOrderId,
    bomId: props.bomId
  }),
  async ({ orderId, bomId }) => {
    // 1. 重置表单
    formData.value = []
    
    // 2. 订单ID非空则加载数据
    if (orderId) {
      try {
        formLoading.value = true
        const workOrderDetails = await WorkOrderApi.getWorkOrderDetailListByBizOrderId(orderId)

        // 使用 setDetails 方法确保数据格式一致，包括规格字段的正确映射
        if (workOrderDetails && Array.isArray(workOrderDetails) && workOrderDetails.length > 0) {
          setDetails(workOrderDetails)
        } else {
          formData.value = []
        }

        // 3. 如果有BOM ID，可以在此处做进一步处理
        if (bomId) {
          // 例如：加载BOM明细并填充到表单
        }

        // 确保所有数据都有正确的序号
        reorderSequenceNumbers()

        // 数据加载完成后，触发完整的重新计算
        await nextTick()
        triggerFullRecalculation()
      } finally {
        formLoading.value = false
      }
    } else if (bomId) {
      // 4. 如果只有BOM ID，可能需要根据BOM ID加载数据
      const bomDetailList = await getMaterialsByBomId(bomId, '')
      formData.value = bomDetailList.map(item => ({
        ...item,
        bizOrderId: props.bizOrderId,
        unit: item.materialUnit,
        slotQuantity: item.quantity || 0,
        quantity: calculateRowTotalQuantity({ // 立即计算
          slotQuantity: item.quantity || 0
        })
      }))

      // BOM数据加载完成后，触发完整的重新计算
      nextTick(() => {
        triggerFullRecalculation()
      })
    }
  },
  { immediate: true }
)

/** 新增按钮操作 */
const handleAdd = () => {
  // 计算新行的序号
  const nextNum = formData.value.length > 0
    ? Math.max(...formData.value.map(item => Number(item.num) || 0)) + 1
    : 1;

  const row = {
    // 基本字段
    id: undefined,
    num: nextNum, // 自动生成序号
    bizOrderId: undefined as number | undefined,
    bizOrderNo: undefined,

    // 仓库和库位
    warehouseId: undefined,
    locationId: undefined,

    // 物料信息
    materialId: undefined,
    materialName: undefined,
    materialCode: undefined,
    spec: undefined,
    unit: undefined,

    // 价格和金额
    unitPrice: undefined,
    amount: undefined,

    // 数量信息
    quantity: undefined,
    plannedQuantity: undefined,
    fulfilledQuantity: undefined,
    standardPlannedQuantity: undefined,
    standardFulfilledQuantity: undefined,
    standardUnit: undefined,
    plannedSpecQuantity: undefined,
    fulfilledSpecQuantity: undefined,

    // 每槽数量相关
    slotQuantity: 0,        // 初始化每槽用量
    slotSpecQuantity: 0,    // 初始化每槽规格用量

    // 锁定数量相关
    lockQuantity: undefined,
    lockTransitQuantity: undefined,

    // 库存相关字段
    readyQuantity: undefined,
    stockQuantity: undefined,
    shortageQuantity: undefined,
    purchaseQuantity: undefined,
    transitQuantity: undefined,
    lockStockQuantity: undefined,

    // 状态相关
    readyStatus: undefined,
    lossRate: undefined,
    lossQuantity: undefined,

    // 税务信息
    taxPrice: undefined,
    taxAmount: undefined,

    // 发票信息
    invoiceQuantity: undefined,
    invoiceAmount: undefined,
    standardInvoiceQuantity: undefined,

    // 其他信息
    note: undefined,
    batchNo: undefined,
    remark: undefined,

    // 成本对象
    costObjectId: undefined,
    costObjectName: undefined,

    // 会计凭证
    accountingVoucherNumber: undefined,

    // 金蝶相关
    kdId: undefined,
    kdOrderId: undefined,

    // 审计字段
    createTime: undefined,
    updateTime: undefined,
    creator: undefined,
    updater: undefined,
    deleted: undefined,
  }
  row.bizOrderId = props.bizOrderId
  formData.value.push(row)
}

/** 删除按钮操作 */
const handleDelete = (index: number) => {
  formData.value.splice(index, 1)
  // 删除后重新排序序号
  reorderSequenceNumbers()
}

/** 重新排序序号 */
const reorderSequenceNumbers = () => {
  formData.value.forEach((item, index) => {
    item.num = index + 1
  })
}

/** 表单校验 */
const validate = () => {
  return formRef.value.validate()
}

/** 表单值 */
const getData = () => {
  return formData.value
}

// 计算总槽数
const calculateTotalSlotCount = (): number => {
  const orderQuantity = props.orderQuantity || 0
  const slotQuantity = (props.slotQuantity && props.slotQuantity > 0) ? props.slotQuantity : BUSINESS_RULES_CONFIG.DEFAULT_SLOT_QUANTITY

  return calculateTotalSlots(orderQuantity, slotQuantity)
}

// 计算单行总用量 (支持传入整个row对象或slotQuantity数值)
const calculateRowTotalQuantity = (rowOrValue: RowData | number): number => {
  const rowSlotQuantity = typeof rowOrValue === 'object'
    ? (rowOrValue.slotQuantity || 0)
    : (rowOrValue || 0)

  const totalSlotCount = calculateTotalSlotCount()
  const totalQuantity = rowSlotQuantity * totalSlotCount

  return formatToPrecision(totalQuantity, CALCULATION_CONFIG.QUANTITY_PRECISION)
}

// 使用导入的规格解析函数，避免重复代码
const extractSpecInfo = parseSpecification

// 获取物料单位名称
const getMaterialUnitName = (unitId: number): string => {
  // 如果没有传入有效的单位ID，返回默认值
  if (!unitId) {
    return '个'
  }

  // 如果单位列表还未加载，返回默认单位
  if (!unitList.value || unitList.value.length === 0) {
    return getDefaultUnitName(unitId)
  }

  const unit = unitList.value.find(u => u.id === unitId)
  const unitName = unit ? unit.name : ''

  // 如果没有找到单位名称，返回默认值
  if (!unitName) {
    return getDefaultUnitName(unitId)
  }

  return unitName
}

// 根据单位ID获取单位名称（从后端数据中查找）
const getDefaultUnitName = (unitId: number): string => {
  // 优先从后端获取的单位列表中查找
  if (unitList.value && unitList.value.length > 0) {
    const unit = unitList.value.find(u => u.id === unitId)
    if (unit && unit.name) {
      return unit.name
    }
  }

  // 如果后端数据中找不到，返回通用默认值
  return '个'
}

// 智能计算每槽规格用量（同时考虑物料单位和规格信息，支持余数显示）
const calculateSlotSpecQuantity = (row: RowData): string | number => {
  const specInfo = extractSpecInfo(row.spec || '')
  const slotQuantity = row.slotQuantity || 0

  // 获取物料单位名称，如果单位列表未加载完成，先返回数值
  let materialUnitName = ''
  if (unitList.value && unitList.value.length > 0 && row.unit) {
    materialUnitName = getMaterialUnitName(row.unit)
  } else if (row.unit) {
    // 单位列表未加载完成时，先返回数值，等待后续重新计算
    return slotQuantity || 1
  } else {
    materialUnitName = '个'
  }

  const materialUnitInfo = getUnitConversionFactor(materialUnitName)

  // 如果没有规格信息或规格值为0，使用默认逻辑
  if (!specInfo.value || specInfo.value <= 0) {
    return slotQuantity || 1
  }

  // 使用新的计算函数，支持余数显示
  const result = calculateSpecQuantityWithRemainder(slotQuantity, specInfo, materialUnitInfo, materialUnitName)

  // 始终返回显示文本，包含完整的单位信息
  return result.displayText
}

// 计算单行总规格用量（考虑包装规格转换，支持字符串格式）
const calculateRowTotalSpecQuantity = (row: RowData): string | number => {
  const totalSlotCount = calculateTotalSlotCount()

  // 如果没有设置每槽规格用量，智能计算
  if (row.slotSpecQuantity === undefined || row.slotSpecQuantity === null || row.slotSpecQuantity === 0) {
    row.slotSpecQuantity = calculateSlotSpecQuantity(row)
  }

  // 处理字符串类型的每槽规格用量
  if (typeof row.slotSpecQuantity === 'string') {
    // 如果每槽规格用量是字符串格式（如 "6袋+19千克"），则总规格用量也用字符串表示
    const specInfo = extractSpecInfo(row.spec || '')
    const materialUnitName = getMaterialUnitName(row.unit || 0)
    const materialUnitInfo = getUnitConversionFactor(materialUnitName)

    // 重新计算总规格用量
    const totalResult = calculateSpecQuantityWithRemainder(
      (row.slotQuantity || 0) * totalSlotCount,
      specInfo,
      materialUnitInfo,
      materialUnitName
    )
    return totalResult.displayText
  } else {
    // 数字类型的计算，也使用新的计算函数获取带单位的显示文本
    const specInfo = extractSpecInfo(row.spec || '')
    const materialUnitName = getMaterialUnitName(row.unit || 0)
    const materialUnitInfo = getUnitConversionFactor(materialUnitName)

    // 重新计算总规格用量，确保显示单位
    const totalResult = calculateSpecQuantityWithRemainder(
      (row.slotQuantity || 0) * totalSlotCount,
      specInfo,
      materialUnitInfo,
      materialUnitName
    )

    return totalResult.displayText
  }
}

// 重新计算所有行的数据
const recalculateAll = () => {
  if (formData.value && Array.isArray(formData.value)) {
    formData.value.forEach(row => {
      if (row) {
        // 重新计算每槽规格用量（确保使用正确的单位名称）
        row.slotSpecQuantity = calculateSlotSpecQuantity(row)
        // 重新计算总用量和总规格用量
        row.quantity = calculateRowTotalQuantity(row)
        row.plannedSpecQuantity = calculateRowTotalSpecQuantity(row)
      }
    })
  }
}

// 触发完整的重新计算
const triggerFullRecalculation = () => {
  recalculateAll()
}

// 处理每槽用量输入
const handleSlotQuantityChange = (row: RowData): void => {
  row.quantity = calculateRowTotalQuantity(row)
  // 如果每槽规格用量为空，重新计算规格用量
  if (row.slotSpecQuantity === undefined || row.slotSpecQuantity === null || row.slotSpecQuantity === 0) {
    row.slotSpecQuantity = calculateSlotSpecQuantity(row)
    row.plannedSpecQuantity = calculateRowTotalSpecQuantity(row)
  } else {
    row.plannedSpecQuantity = calculateRowTotalSpecQuantity(row)
  }
}

// 监听主表参数变化
watch(() => [props.slotQuantity, props.orderQuantity, props.orderSpecQuantity], () => {
  if (formData.value && Array.isArray(formData.value)) {
    formData.value.forEach(row => {
      if (row) {
        row.quantity = calculateRowTotalQuantity(row)
        row.plannedSpecQuantity = calculateRowTotalSpecQuantity(row)
      }
    })
  }
}, { immediate: true })

/** 表格汇总方法 */
const getSummaries = (param: any) => {
  const { columns, data } = param
  const sums: string[] = []

  columns.forEach((column: any, index: number) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }

    // 根据列的property属性进行汇总，而不是依赖索引
    const property = column.property

    // 每槽数量汇总
    if (property === 'slotQuantity') {
      const values = data.map((item: any) => Number(item.slotQuantity) || 0)
      if (!values.every((value: number) => Number.isNaN(value))) {
        const total = values.reduce((prev: number, curr: number) => {
          const value = Number(curr)
          if (!Number.isNaN(value)) {
            return prev + value
          } else {
            return prev
          }
        }, 0)
        sums[index] = formatQuantity(total)
      } else {
        sums[index] = ''
      }
    }
    // 每槽规格数量汇总
    else if (property === 'slotSpecQuantity') {
      const values = data.map((item: any) => Number(item.slotSpecQuantity) || 0)
      if (!values.every((value: number) => Number.isNaN(value))) {
        const total = values.reduce((prev: number, curr: number) => {
          const value = Number(curr)
          if (!Number.isNaN(value)) {
            return prev + value
          } else {
            return prev
          }
        }, 0)
        sums[index] = formatQuantity(total)
      } else {
        sums[index] = ''
      }
    }
    // 数量汇总
    else if (property === 'quantity') {
      const values = data.map((item: any) => Number(item.quantity) || 0)
      if (!values.every((value: number) => Number.isNaN(value))) {
        const total = values.reduce((prev: number, curr: number) => {
          const value = Number(curr)
          if (!Number.isNaN(value)) {
            return prev + value
          } else {
            return prev
          }
        }, 0)
        sums[index] = formatQuantity(total)
      } else {
        sums[index] = ''
      }
    }
    // 计划数量汇总
    else if (property === 'plannedQuantity') {
      const values = data.map((item: any) => Number(item.plannedQuantity) || 0)
      if (!values.every((value: number) => Number.isNaN(value))) {
        const total = values.reduce((prev: number, curr: number) => {
          const value = Number(curr)
          if (!Number.isNaN(value)) {
            return prev + value
          } else {
            return prev
          }
        }, 0)
        sums[index] = formatQuantity(total)
      } else {
        sums[index] = ''
      }
    }else {
      // 其他列不显示汇总信息
      sums[index] = ''
    }
  })

  return sums
}

const warehouseTree = ref<any[]>([]) // 仓库树形列表
const locationList = ref<any[]>([]) // 库位列表
const unitList = ref<any[]>([]) // 单位列表
const getWarehouseTree = async () => {
  const data = await WarehouseApi.getWarehouseList({})
  warehouseTree.value = handleTree(data, 'id', 'parentId')
}
const getLocationList = async (warehouseId: string | number) => {
  locationList.value = await WarehouseLocationApi.getWarehouseLocationPage({ warehouseId: warehouseId || '' })
}
const getUnitList = async () => {
  // 优先使用父组件传递的单位数据，避免重复请求
  if (props.unitList && props.unitList.length > 0) {
    // 将父组件的单位数据格式转换为当前组件需要的格式
    unitList.value = props.unitList.map(item => ({
      id: item.value || item.id,
      name: item.label || item.name
    }))
    console.log('使用父组件传递的单位数据:', unitList.value)
    return
  }

  // 如果父组件没有传递单位数据，则从接口获取
  const resp = await UnitApi.getUnitPage({
    pageNo: 1,
    pageSize: 100,
  })
  unitList.value = resp.list
  console.log('从接口获取单位数据:', unitList.value)
}
onMounted(async () => {
  await getWarehouseTree()
  // await getLocationList()
  await getUnitList()

  // 单位列表加载完成后，触发重新计算以确保使用正确的单位名称
  await nextTick()
  triggerFullRecalculation()
})
const getMaterialsByBomId = async (bomId: number, type: string) => {
  const bomMaterials = await BomApi.getBomMaterialListByBomId(bomId, type)

  // 映射字段，确保数据结构与表单期望的字段匹配
  return bomMaterials.map((item: any) => ({
    ...item,
    // 将 materialUnit 映射为 unit，确保与表单字段一致
    unit: item.materialUnit || item.unit,
    // 确保其他必要字段存在
    bizOrderId: props.bizOrderId,
    num: item.num || 1,
    plannedQuantity: item.quantity || 0,
    fulfilledQuantity: 0,
    unitPrice: 0,
    amount: 0,
    remark: item.remark || '',
  }))
}
/** 格式化物料选项显示标签 */
const formatMaterialLabel = (material: any) => {
  if (!material) return ''
  const fullCode = material.fullCode || ''
  const name = material.name || ''
  const spec = material.spec || ''

  // 构建显示标签，只有非空值才参与拼接
  const parts: string[] = []
  if (fullCode) parts.push(fullCode)
  if (name) parts.push(name)
  if (spec) parts.push(spec)

  return parts.join(' - ')
}

const getRemoteMaterial = async (params: any) => {
  const { pageNo, pageSize, query, ...restParams } = params;
  const response = await MaterialApi.getSimpleMaterialPage({
    pageNo,
    pageSize,
    fullName: query, // 映射到 API 的 `name` 字段
    ...restParams,
  });
  const { list, total } = await response;
  return { list, total };
}

// 设置物料明细数据
const setDetails = (details: any[]) => {
  if (!details || details.length === 0) {
    return
  }
  
  // 清空现有数据
  formData.value = []
  
  // 映射物料明细数据到表单数据格式
  const mappedDetails = details.map((detail: any, index: number) => {
    return {
      num: index + 1,
      materialId: detail.materialId,
      materialCode: detail.materialCode,
      materialName: detail.materialName,
      spec: detail.spec || detail.materialSpec,
      unit: detail.unit,
      quantity: detail.pendingQuantity || detail.quantity || detail.plannedQuantity,
      plannedQuantity: detail.plannedQuantity || detail.pendingQuantity || detail.quantity,
      fulfilledQuantity: detail.fulfilledQuantity || 0,
      standardUnit: detail.standardUnit || detail.standardUnitId || detail.unit,
      standardPlannedQuantity: detail.standardPlannedQuantity || detail.plannedQuantity || detail.pendingQuantity || detail.quantity,
      standardFulfilledQuantity: detail.standardFulfilledQuantity || 0,
      plannedSpecQuantity: detail.plannedSpecQuantity || detail.slotSpecQuantity || 0,
      fulfilledSpecQuantity: detail.fulfilledSpecQuantity || 0,

      // 每槽数量相关字段
      slotQuantity: detail.slotQuantity || 0,
      slotSpecQuantity: detail.slotSpecQuantity || 0,

      // 库存相关字段
      readyQuantity: detail.readyQuantity || 0,
      stockQuantity: detail.stockQuantity || 0,
      shortageQuantity: detail.shortageQuantity || 0,
      purchaseQuantity: detail.purchaseQuantity || 0,
      transitQuantity: detail.transitQuantity || 0,
      lockStockQuantity: detail.lockStockQuantity || 0,
      lockTransitQuantity: detail.lockTransitQuantity || 0,
      lockQuantity: detail.lockQuantity || 0,

      // 状态字段
      readyStatus: detail.readyStatus || 0,
      lossRate: detail.lossRate || 0,
      lossQuantity: detail.lossQuantity || 0,

      // 价格字段
      unitPrice: detail.unitPrice || 0,
      amount: detail.amount || 0,

      // 其他字段
      remark: detail.remark || detail.requirement || '',
      note: detail.note || '',
      batchNo: detail.batchNo || '',
      warehouseId: detail.warehouseId,
      locationId: detail.locationId,
      bizOrderId: props.bizOrderId,

      // 版本和类型字段
      version: detail.version || 1,
      materialType: detail.materialType
    }
  })

  // 设置表单数据
  formData.value = mappedDetails
}

defineExpose({ validate, getData, getMaterialsByBomId, setDetails, recalculateAll })
</script>

<style lang="scss" scoped>
.material-info {
  .material-code,
  .material-spec,
  .material-name{
    display: flex;
    align-items: center;
    font-size: 12px;
    line-height: 1.4;
    margin-bottom: 4px;

    .label {
      color: #909399;
      font-weight: 500;
      min-width: 36px;
    }

    .value {
      color: #606266;
      flex: 1;
    }
  }

  .material-spec {
    margin-bottom: 0;
  }
}
</style>
